<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Customer;
use App\Models\Invoice;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class AgenController extends Controller
{
    public function index(Request $request)
    {
        $agen = Auth::user()->id;

        // Get current month as default filter
        $currentMonth = Carbon::now()->format('m'); // Format: 01, 02, 03, etc.
        $filterMonth = $request->get('month', $currentMonth);

        // Get customers with invoices, filtered by month
        $query = Customer::with(['invoice' => function($q) use ($filterMonth) {
                $q->with(['status', 'pembayaran.user']);

                // Filter invoices by month if not showing all
                if ($filterMonth !== 'all') {
                    $q->whereRaw("MONTH(STR_TO_DATE(jatuh_tempo, '%Y-%m-%d')) = ?", [intval($filterMonth)]);
                }

                $q->orderBy('jatuh_tempo', 'desc');
            }, 'paket'])
            ->where('agen_id', $agen)
            ->whereIn('status_id', [3, 9])
            ->whereHas('invoice', function($q) use ($filterMonth) {
                // Only include customers who have invoices in the selected month
                if ($filterMonth !== 'all') {
                    $q->whereRaw("MONTH(STR_TO_DATE(jatuh_tempo, '%Y-%m-%d')) = ?", [intval($filterMonth)]);
                }
            });
            // dd($query);
        // Apply search filter if provided
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('nama_customer', 'LIKE', "%{$search}%")
                  ->orWhere('alamat', 'LIKE', "%{$search}%")
                  ->orWhere('no_hp', 'LIKE', "%{$search}%");
            });
        }

        $customers = $query->paginate(10);

        // Get available months for filter dropdown
        $availableMonths = $this->getAvailableMonths($agen);

        // Get current month name in Indonesian
        $currentMonthName = $this->getIndonesianMonthName($currentMonth);

        // If this is an AJAX request, return JSON
        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'data' => $customers,
                'html' => view('agen.partials.customer-table-rows', compact('customers'))->render()
            ]);
        }

        return view('agen.data-pelanggan-agen',[
            'users' => Auth::user(),
            'roles' => Auth::user()->roles,
            'customers' => $customers,
            'availableMonths' => $availableMonths,
            'currentMonth' => $currentMonth,
            'currentMonthName' => $currentMonthName,
            'selectedMonth' => $filterMonth,
        ]);
    }

    public function search(Request $request)
    {
        $agen = Auth::user()->id;

        // Get month filter (default to current month)
        $currentMonth = Carbon::now()->format('m');
        $filterMonth = $request->get('month', $currentMonth);

        // Get customers with invoices, filtered by month
        $query = Customer::with(['invoice' => function($q) use ($filterMonth) {
                $q->with('status');

                // Filter invoices by month if not showing all
                if ($filterMonth !== 'all') {
                    $q->whereRaw("MONTH(STR_TO_DATE(jatuh_tempo, '%Y-%m-%d')) = ?", [intval($filterMonth)]);
                }

                $q->orderBy('jatuh_tempo', 'desc');
            }, 'paket'])
            ->where('agen_id', $agen)
            ->whereIn('status_id', [3, 9])
            ->whereHas('invoice', function($q) use ($filterMonth) {
                // Only include customers who have invoices in the selected month
                if ($filterMonth !== 'all') {
                    $q->whereRaw("MONTH(STR_TO_DATE(jatuh_tempo, '%Y-%m-%d')) = ?", [intval($filterMonth)]);
                }
            });

        // Apply search filter
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('nama_customer', 'LIKE', "%{$search}%")
                  ->orWhere('alamat', 'LIKE', "%{$search}%")
                  ->orWhere('no_hp', 'LIKE', "%{$search}%");
            });
        }

        $customers = $query->paginate(10);

        return response()->json([
            'success' => true,
            'data' => $customers->items(),
            'pagination' => [
                'current_page' => $customers->currentPage(),
                'last_page' => $customers->lastPage(),
                'per_page' => $customers->perPage(),
                'total' => $customers->total(),
            ]
        ]);
    }

    /**
     * Get available months from invoices for filter dropdown
     */
    private function getAvailableMonths($agenId)
    {
        $invoices = Invoice::whereHas('customer', function($q) use ($agenId) {
                $q->where('agen_id', $agenId)
                  ->whereIn('status_id', [3, 9]);
            })
            ->whereNotNull('jatuh_tempo')
            ->get();

        $months = [];
        foreach ($invoices as $invoice) {
            try {
                $date = Carbon::parse($invoice->jatuh_tempo);
                $monthNum = $date->format('m');
                $monthName = $this->getIndonesianMonthName($monthNum);
                $months[$monthNum] = $monthName;
            } catch (\Exception $e) {
                // Skip invalid dates
                continue;
            }
        }

        // Sort by month number
        ksort($months);

        return $months;
    }

    /**
     * Get Indonesian month name from month number
     */
    private function getIndonesianMonthName($monthNumber)
    {
        $months = [
            '01' => 'Januari', '02' => 'Februari', '03' => 'Maret',
            '04' => 'April', '05' => 'Mei', '06' => 'Juni',
            '07' => 'Juli', '08' => 'Agustus', '09' => 'September',
            '10' => 'Oktober', '11' => 'November', '12' => 'Desember'
        ];

        return $months[$monthNumber] ?? 'Unknown';
    }
}
